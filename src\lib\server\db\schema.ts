import { pgTable, serial, text, integer, uuid, varchar, vector, real } from 'drizzle-orm/pg-core';

export const characterTable = pgTable("character", {
	id: serial().primary<PERSON>ey(),
	name: varchar({length: 256}).notNull(),
	referenceCurve: vector({dimensions: 1}).notNull(),
	targetLength: real().notNull(),
	offsetPos: real().notNull(), // this should be an array
	offsetScale: real().notNull(), // this should be an array
});